# XAMPP AI Todo App (Local Edition with OpenRouter)

## 1. Local Setup with XAMPP

### Project Structure:
```
xampp/
└── htdocs/
    └── ai-todo-app/
        ├── backend/
        │   ├── database/  # SQLite files
        │   ├── public/
        │   ├── src/
        │   └── server.js
        ├── frontend/
        │   ├── public/
        │   └── src/
        ├── models/  # For local AI model files if needed
        └── .env     # For API keys and configuration
```

### XAMPP Configuration:

1. Place the `ai-todo-app` folder in XAMPP's `htdocs` directory
2. Set up virtual hosts:
   ```apache
   <VirtualHost *:80>
       ServerName todo-app.local
       DocumentRoot "C:/xampp/htdocs/ai-todo-app"
   </VirtualHost>
   ```
3. Map `todo-app.local` to `127.0.0.1` in your hosts file
4. Start Apache and MySQL services in XAMPP control panel

## 2. Core Architecture

### Tech Stack:
- **Frontend**: React.js (served from XAMPP)
- **Backend API**: Node.js/Express.js (run via terminal)
- **Database**: SQLite (file-based, stored in backend/database/)
- **AI**: OpenRouter API for natural language processing
- **Authentication**: JWT + local Passport.js strategy

## 3. AI Integration with OpenRouter

### Setup:
1. Register and get API keys from OpenRouter
2. Add API keys to .env file in your project root

### Usage:
- For task parsing and NLP:
  ```javascript
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`
    },
    body: JSON.stringify({
      model: 'openai/gpt-3.5-turbo',
      messages: [{
        role: 'user',
        content: 'Parse this task: Buy groceries...'
      }]
    })
  });
  ```

## 4. Key Features

### Local Processing:
- SQLite for task storage
- Local file attachments
- Offline mode with sync capabilities

### AI-Driven:
- Intelligent task parsing via OpenRouter API
- Natural language queries and commands
- Smart reminders and deadline suggestions
- Sentiment analysis for task prioritization

## 5. Development Roadmap

### Phase 1: Core Functionality
1. Set up React frontend in XAMPP
2. Create Node.js backend API
3. Implement basic CRUD operations with SQLite
4. Set up authentication with JWT

### Phase 2: AI Integration
1. Integrate OpenRouter API
2. Implement task parsing
3. Add voice command support

### Phase 3: Advanced Features
1. Location-based reminders
2. Cross-device synchronization
3. Browser extension integration

## 6. Local Development Workflow

1. Start XAMPP and Apache service
2. Run
   ```shell
   npm run dev
   ```
   in both `frontend/` and `backend/` directories
3. Access at http://todo-app.local:3000

## 7. Migration to Production
1. Dockerize the application
2. Set up Forewindows service for backend
3. Configure SSL for local network usage
